import { EventContext } from '@cloudflare/workers-types';
import { Env } from './types';
import moment from 'moment-timezone';

// 导入 cron 任务处理器
import { onRequest as resetAccountsCron } from './newaccount/reset-accounts-cron';
import { onRequest as initAccountsCron } from './newaccount/init-accounts-cron';

/**
 * Cloudflare Workers 调度事件处理器
 * 根据当前时间调用相应的 cron 任务
 */
export default {
    async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
        try {
            // 获取当前时间
            const currentTime = moment().tz('Asia/Shanghai');
            const currentHour = currentTime.hour();
            const currentMinute = currentTime.minute();
            const formattedTime = currentTime.format('YYYY-MM-DD HH:mm:ss');

            console.log(`[Scheduled Event] Triggered at ${formattedTime} (Hour: ${currentHour}, Minute: ${currentMinute})`);

            // 创建模拟的 EventContext 用于调用 cron 任务
            const createMockContext = (taskType: 'reset' | 'init'): EventContext<Env, string, Record<string, unknown>> => ({
                request: new Request(`https://example.com/newaccount/${taskType}-accounts-cron`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                }),
                env,
                params: {},
                data: {},
                waitUntil: ctx.waitUntil.bind(ctx),
                passThroughOnException: () => { },
                next: async () => new Response('Not implemented'),
                functionPath: `/newaccount/${taskType}-accounts-cron` // 对应实际的 cron 任务路径
            });
            let taskExecuted = false;
            let taskResult: Response | null = null;

            // 早上8点执行重置账号任务
            if (currentHour === 8 && currentMinute === 0) {
                console.log('[Scheduled Event] Executing reset accounts cron task');
                try {
                    taskResult = await resetAccountsCron(createMockContext('reset'));
                    taskExecuted = true;
                    console.log('[Scheduled Event] Reset accounts cron task completed');
                } catch (error) {
                    console.error('[Scheduled Event] Reset accounts cron task failed:', error);
                }
            }
            // 晚上20点执行初始化账号任务
            else if (currentHour === 20 && currentMinute === 0) {
                console.log('[Scheduled Event] Executing init accounts cron task');
                try {
                    taskResult = await initAccountsCron(createMockContext('init'));
                    taskExecuted = true;
                    console.log('[Scheduled Event] Init accounts cron task completed');
                } catch (error) {
                    console.error('[Scheduled Event] Init accounts cron task failed:', error);
                }
            }

            // 记录执行结果
            if (taskExecuted && taskResult) {
                const resultText = await taskResult.text();
                console.log(`[Scheduled Event] Task result (${taskResult.status}):`, resultText);
            } else if (!taskExecuted) {
                console.log(`[Scheduled Event] No task scheduled for ${currentHour}:${currentMinute.toString().padStart(2, '0')}`);
            }

        } catch (error) {
            console.error('[Scheduled Event] Error in scheduled handler:', error);
            // 不抛出错误，避免影响其他调度任务
        }
    }
};

/**
 * 手动触发调度任务的 HTTP 端点（用于测试）
 */
export const onRequest = async (context: EventContext<Env, string, Record<string, unknown>>): Promise<Response> => {
    const request = context.request;
    const env = context.env as Env;

    // 只允许 POST 请求
    if (request.method !== 'POST') {
        return new Response(JSON.stringify({
            error: 'Method not allowed. Use POST to manually trigger scheduled tasks.'
        }), {
            status: 405,
            headers: { 'Content-Type': 'application/json' }
        });
    }

    try {
        const body = await request.json() as { task?: string; force?: boolean };
        const currentTime = moment().tz('Asia/Shanghai');
        const currentHour = currentTime.hour();
        const formattedTime = currentTime.format('YYYY-MM-DD HH:mm:ss');

        let taskResult: Response;

        if (body.task === 'reset-accounts' || (body.force && currentHour === 8)) {
            console.log('[Manual Trigger] Executing reset accounts cron task');
            taskResult = await resetAccountsCron(context);
        } else if (body.task === 'init-accounts' || (body.force && currentHour === 20)) {
            console.log('[Manual Trigger] Executing init accounts cron task');
            taskResult = await initAccountsCron(context);
        } else {
            return new Response(JSON.stringify({
                error: 'Invalid task specified. Use "reset-accounts" or "init-accounts"',
                availableTasks: ['reset-accounts', 'init-accounts'],
                currentTime: formattedTime,
                currentHour
            }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }

        const resultText = await taskResult.text();
        const resultData = JSON.parse(resultText);

        return new Response(JSON.stringify({
            success: true,
            message: 'Manual task execution completed',
            taskResult: resultData,
            executedAt: formattedTime
        }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
        });

    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        console.error('[Manual Trigger] Error:', error);

        return new Response(JSON.stringify({
            success: false,
            error: errorMessage,
            timestamp: moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
};
